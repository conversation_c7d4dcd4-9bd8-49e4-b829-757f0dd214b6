#!/usr/bin/env python3
"""
测试Redis搜索会话存储功能
"""

import asyncio
import json
from datetime import datetime
from app.core.redis_manager import redis_manager
from app.core.search_session_manager import search_session_manager

async def test_redis_sessions():
    """测试Redis搜索会话存储功能"""
    print("开始测试Redis搜索会话存储...")
    
    try:
        # 初始化Redis连接
        print("1. 初始化Redis连接...")
        await redis_manager.initialize()
        print("   ✓ Redis连接成功")
        
        # 初始化搜索会话管理器
        print("2. 初始化搜索会话管理器...")
        await search_session_manager.initialize()
        storage_type = "Redis" if search_session_manager.is_using_redis() else "内存"
        print(f"   ✓ 搜索会话管理器初始化成功 - 使用{storage_type}存储")
        
        # 测试基本操作
        print("3. 测试基本存储操作...")
        test_search_id = "test_search_123"
        test_data = {
            "search_id": test_search_id,
            "request": {
                "origin_iata": "PEK",
                "destination_iata": "LAX",
                "departure_date_from": "2024-03-01"
            },
            "phase": "phase_one",
            "status": "processing",
            "started_at": datetime.now().isoformat()
        }
        
        # 存储会话
        success = await search_session_manager.set_session(test_search_id, test_data, 300)
        print(f"   ✓ 存储会话: {'成功' if success else '失败'}")
        
        # 读取会话
        retrieved_data = await search_session_manager.get_session(test_search_id)
        print(f"   ✓ 读取会话: {'成功' if retrieved_data else '失败'}")
        if retrieved_data:
            print(f"     - 搜索ID: {retrieved_data.get('search_id')}")
            print(f"     - 状态: {retrieved_data.get('status')}")
            print(f"     - 阶段: {retrieved_data.get('phase')}")
        
        # 更新会话
        updates = {
            "status": "completed",
            "results_count": 25,
            "completed_at": datetime.now().isoformat()
        }
        update_success = await search_session_manager.update_session(test_search_id, updates)
        print(f"   ✓ 更新会话: {'成功' if update_success else '失败'}")
        
        # 验证更新
        updated_data = await search_session_manager.get_session(test_search_id)
        if updated_data:
            print(f"     - 更新后状态: {updated_data.get('status')}")
            print(f"     - 结果数量: {updated_data.get('results_count')}")
        
        # 测试会话信息
        print("4. 测试会话信息获取...")
        session_info = await search_session_manager.get_session_info(test_search_id)
        if session_info:
            print(f"   ✓ 会话信息获取成功")
            print(f"     - 存储类型: {session_info.get('storage_type')}")
            print(f"     - 创建时间: {session_info.get('created_at')}")
            print(f"     - TTL: {session_info.get('ttl_seconds')}秒")
        
        # 测试会话列表
        print("5. 测试会话列表...")
        sessions = await search_session_manager.list_sessions("test_*")
        print(f"   ✓ 找到 {len(sessions)} 个测试会话")
        
        # 测试健康检查
        print("6. 测试健康检查...")
        health = await search_session_manager.health_check()
        print(f"   ✓ 健康检查: {health.get('status')}")
        print(f"     - 存储类型: {health.get('storage_type')}")
        print(f"     - Redis可用: {health.get('redis_available')}")
        
        # 清理测试数据
        print("7. 清理测试数据...")
        delete_success = await search_session_manager.delete_session(test_search_id)
        print(f"   ✓ 删除会话: {'成功' if delete_success else '失败'}")
        
        # 验证删除
        exists = await search_session_manager.exists(test_search_id)
        print(f"   ✓ 验证删除: {'会话已删除' if not exists else '会话仍存在'}")
        
        print("\n✅ 所有测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 关闭连接
        print("\n8. 关闭连接...")
        await redis_manager.close()
        print("   ✓ 连接已关闭")

async def test_concurrent_sessions():
    """测试并发会话处理"""
    print("\n开始测试并发会话处理...")
    
    try:
        await redis_manager.initialize()
        await search_session_manager.initialize()
        
        # 创建多个并发会话
        tasks = []
        for i in range(10):
            search_id = f"concurrent_test_{i}"
            session_data = {
                "search_id": search_id,
                "request": {"test": f"data_{i}"},
                "status": "processing",
                "started_at": datetime.now().isoformat()
            }
            task = search_session_manager.set_session(search_id, session_data)
            tasks.append(task)
        
        # 等待所有会话创建完成
        results = await asyncio.gather(*tasks)
        success_count = sum(1 for r in results if r)
        print(f"   ✓ 并发创建 {success_count}/10 个会话成功")
        
        # 并发读取所有会话
        read_tasks = [
            search_session_manager.get_session(f"concurrent_test_{i}")
            for i in range(10)
        ]
        read_results = await asyncio.gather(*read_tasks)
        read_success_count = sum(1 for r in read_results if r)
        print(f"   ✓ 并发读取 {read_success_count}/10 个会话成功")
        
        # 清理并发测试数据
        cleanup_tasks = [
            search_session_manager.delete_session(f"concurrent_test_{i}")
            for i in range(10)
        ]
        await asyncio.gather(*cleanup_tasks)
        print("   ✓ 并发测试数据已清理")
        
    except Exception as e:
        print(f"❌ 并发测试失败: {e}")
    
    finally:
        await redis_manager.close()

if __name__ == "__main__":
    print("Redis搜索会话存储测试")
    print("=" * 50)
    
    # 运行基本测试
    asyncio.run(test_redis_sessions())
    
    # 运行并发测试
    asyncio.run(test_concurrent_sessions())
    
    print("\n测试完成！")
