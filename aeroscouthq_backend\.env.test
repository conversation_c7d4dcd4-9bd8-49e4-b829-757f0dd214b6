# Database - Use a separate test database
DATABASE_URL=sqlite+aiosqlite:///./test_aeroscout.db

# JWT
SECRET_KEY=your_secret_key_here_please_change_me
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# API Limits - Specific limits per API type
POI_DAILY_LIMIT=100
FLIGHT_DAILY_LIMIT=50

# Dynamic Fetcher Cache Files
TRIP_COOKIE_FILE=trip_cookies_test.json
KIWI_TOKEN_FILE=kiwi_token_test.json

# Hub Probing
CHINA_HUB_CITIES_FOR_PROBE='["SHA", "PVG", "PEK", "PKX", "CAN", "CTU", "TFU", "SZX"]'

# Testing flag - Important for test database setup
TESTING=True

# Cookie Expiry Configuration (in seconds)
TRIP_COOKIE_EXPIRY_SECONDS=3600
KIWI_COOKIE_EXPIRY_SECONDS=3600

# Celery Configuration (Example using Redis)
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/1

# Redis Configuration for Search Sessions
REDIS_URL=redis://localhost:6379/3
REDIS_SESSION_TTL=1800
